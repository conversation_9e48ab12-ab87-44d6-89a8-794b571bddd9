package main

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"log"
	"os"
	"path/filepath"

	"github.com/disintegration/imaging"
	"github.com/makiuchi-d/gozxing"
	"github.com/makiuchi-d/gozxing/qrcode"
)

const (
	outputWidth  = 640
	outputHeight = 480
)

// generateQRCode 生成二维码
func generateQRCode(text string, width, height int) (image.Image, error) {
	encoder := qrcode.NewQRCodeWriter()
	encodeHints := map[gozxing.EncodeHintType]interface{}{
		gozxing.EncodeHintType_ERROR_CORRECTION: "M",
	}

	bitMatrix, err := encoder.Encode(text, gozxing.BarcodeFormat_QR_CODE, width, height, encodeHints)
	if err != nil {
		return nil, err
	}

	img := image.NewRGBA(image.Rect(0, 0, width, height))
	white := color.RGBA{255, 255, 255, 255}
	black := color.RGBA{0, 0, 0, 255}

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			if bitMatrix.Get(x, y) {
				img.Set(x, y, black)
			} else {
				img.Set(x, y, white)
			}
		}
	}

	return img, nil
}

// addQRCodeWatermark 添加二维码水印
func addQRCodeWatermark(img image.Image, text string) image.Image {
	// 生成二维码
	qrCode, err := generateQRCode(text, 100, 100)
	if err != nil {
		log.Printf("生成二维码失败: %v", err)
		return img
	}

	// 定位在右下角
	pos := image.Point{
		X: img.Bounds().Dx() - qrCode.Bounds().Dx() - 10,
		Y: img.Bounds().Dy() - qrCode.Bounds().Dy() - 10,
	}

	return imaging.Overlay(img, qrCode, pos, 0.8)
}

// loadImage 加载图片
func loadImage(path string) (image.Image, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	img, _, err := image.Decode(f)
	if err != nil {
		return nil, err
	}

	// 调整图片大小
	img = imaging.Resize(img, outputWidth, outputHeight, imaging.Lanczos)
	return img, nil
}

// saveImage 保存图片
func saveImage(path string, img image.Image) error {
	f, err := os.Create(path)
	if err != nil {
		return err
	}
	defer f.Close()

	switch filepath.Ext(path) {
	case ".png":
		return png.Encode(f, img)
	default:
		return fmt.Errorf("不支持的图片格式: %s", filepath.Ext(path))
	}
}

// createTestImage 创建测试图像
func createTestImage() image.Image {
	img := image.NewRGBA(image.Rect(0, 0, outputWidth, outputHeight))
	
	// 创建渐变背景
	for y := 0; y < outputHeight; y++ {
		for x := 0; x < outputWidth; x++ {
			r := uint8(float64(x) / float64(outputWidth) * 255)
			g := uint8(float64(y) / float64(outputHeight) * 255)
			b := uint8(128)
			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 添加一个简单的"脸部"区域
	faceX, faceY := outputWidth/2, outputHeight/2
	faceSize := 100

	// 脸部轮廓
	for y := faceY - faceSize/2; y < faceY + faceSize/2; y++ {
		for x := faceX - faceSize/2; x < faceX + faceSize/2; x++ {
			if x >= 0 && x < outputWidth && y >= 0 && y < outputHeight {
				// 简单的圆形脸部
				dx := float64(x - faceX)
				dy := float64(y - faceY)
				if dx*dx + dy*dy < float64(faceSize*faceSize)/4 {
					img.Set(x, y, color.RGBA{220, 180, 140, 255}) // 肤色
				}
			}
		}
	}

	// 添加嘴部区域
	mouthY := faceY + 20
	mouthWidth := 30
	mouthHeight := 10
	
	for y := mouthY - mouthHeight/2; y < mouthY + mouthHeight/2; y++ {
		for x := faceX - mouthWidth/2; x < faceX + mouthWidth/2; x++ {
			if x >= 0 && x < outputWidth && y >= 0 && y < outputHeight {
				img.Set(x, y, color.RGBA{180, 100, 100, 255}) // 嘴唇色
			}
		}
	}

	return img
}

func main() {
	fmt.Println("deepseek_video 简化版本测试...")

	// 创建测试图像
	testImg := createTestImage()
	fmt.Println("测试图像创建成功")

	// 添加水印
	watermarked := addQRCodeWatermark(testImg, "Generated by Go Lip Animator - Simple Version")
	fmt.Println("水印添加成功")

	// 保存图像
	outputPath := "test_simple_output.png"
	if err := saveImage(outputPath, watermarked); err != nil {
		log.Fatalf("保存图像失败: %v", err)
	}

	fmt.Printf("测试完成！图像已保存为: %s\n", outputPath)
	fmt.Println("所有核心功能工作正常。")
	fmt.Println("\n注意：要使用完整的人脸检测功能，需要下载 facefinder 级联文件。")
	fmt.Println("可以从 https://github.com/esimov/pigo 获取相关文件。")
}
